import { noticesForRequestData } from './requestNoticeMockData';

// Используем данные из нового файла
export const allNotices = noticesForRequestData;

export const krg3NoticeForRequestMockData = (
  pageNumber: number,
  pageSize: number,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any => {
  const startIndex = (pageNumber - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pageNotices = allNotices.slice(startIndex, endIndex);
  return {
    columns: [
      {
        title: 'Номер',
        dataIndex: 'number',
        key: 'number',
        columnType: 'String',
        width: 100,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Тема',
        dataIndex: 'subject',
        key: 'subject',
        columnType: 'String',
        width: 300,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Статус',
        dataIndex: 'status',
        key: 'status',
        columnType: 'String',
        width: 150,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Дата создания',
        dataIndex: 'dateCreate',
        key: 'dateCreate',
        columnType: 'String',
        width: 180,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Дата отправки',
        dataIndex: 'dateSend',
        key: 'dateSend',
        columnType: 'String',
        width: 180,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Автор',
        dataIndex: 'userCreate',
        key: 'userCreate',
        columnType: 'String',
        width: 250,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Регистрационный номер',
        dataIndex: 'regNum',
        key: 'regNum',
        columnType: 'String',
        width: 200,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
    ],
    rows: pageNotices,
    pagination: {
      total: allNotices.length,
      pageSize,
    },
    pageNumber,
    sort: null,
    group: null,
  };
};

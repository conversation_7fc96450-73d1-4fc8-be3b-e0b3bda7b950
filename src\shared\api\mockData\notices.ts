// Генерируем данные для уведомлений
export const allNotices = [
  {
    actual: 'Актуально',
    dateReg: '16.11.2022 17:58:15',
    note: 'Уведомление о принятии заявки',
    dateSigning: null,
    dateRelease: null,
    subject: 'Принятие заявки №1',
    dateLastChange: '16.11.2022 17:58:15',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '16.11.2022 17:58:15',
    dateSend: '16.11.2022 18:00:00',
    rowId: {
      isActual: true,
      id: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    },
    number: 6,
    userCreate: 'Кирюхин Евгений Валерьевич',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-001-2022',
    _sortColumn: 6,
    key: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    status: 'Отправлено',
    dateReception: '16.11.2022 18:05:00',
  },
  {
    actual: 'Актуально',
    dateReg: '17.11.2022 09:15:30',
    note: 'Уведомление о дополнительных требованиях',
    dateSigning: null,
    dateRelease: null,
    subject: 'Дополнительные требования к заявке №1',
    dateLastChange: '17.11.2022 09:15:30',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '17.11.2022 09:15:30',
    dateSend: null,
    rowId: {
      isActual: true,
      id: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    },
    number: 7,
    userCreate: 'Петров П.П.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_prepared',
    regNum: 'УВ-002-2022',
    _sortColumn: 7,
    key: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    status: 'Подготовлено',
    dateReception: null,
  },
];

export const krg3NoticeForRequestMockData = (
  pageNumber: number,
  pageSize: number,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any => {
  const startIndex = (pageNumber - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pageNotices = allNotices.slice(startIndex, endIndex);
  return {
    columns: [
      {
        title: 'Номер',
        dataIndex: 'number',
        key: 'number',
        columnType: 'String',
        width: 100,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Тема',
        dataIndex: 'subject',
        key: 'subject',
        columnType: 'String',
        width: 300,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Статус',
        dataIndex: 'status',
        key: 'status',
        columnType: 'String',
        width: 150,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Дата создания',
        dataIndex: 'dateCreate',
        key: 'dateCreate',
        columnType: 'String',
        width: 180,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Дата отправки',
        dataIndex: 'dateSend',
        key: 'dateSend',
        columnType: 'String',
        width: 180,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Автор',
        dataIndex: 'userCreate',
        key: 'userCreate',
        columnType: 'String',
        width: 250,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
      {
        title: 'Регистрационный номер',
        dataIndex: 'regNum',
        key: 'regNum',
        columnType: 'String',
        width: 200,
        align: 'left',
        columnUuid: null,
        filterType: null,
        filters: null,
        sortable: false,
      },
    ],
    rows: pageNotices,
    pagination: {
      total: allNotices.length,
      pageSize,
    },
    pageNumber,
    sort: null,
    group: null,
  };
};

// Моковые данные для requestNotice (ленивые табы)

export const requestNoticeTableData = {
  columns: [
    {
      title: 'Номер',
      dataIndex: 'number',
      key: 'number',
      columnType: 'String',
      width: 100,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Вид',
      dataIndex: 'type',
      key: 'type',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '1',
          value: '1',
          text: 'Заявка',
        },
        {
          key: '2',
          value: '2',
          text: 'Уведомление',
        },
      ],
      sortable: false,
    },
    {
      title: 'Статус',
      dataIndex: 'status',
      key: 'status',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '0',
          value: 'edaceb01-f2a9-3d29-e053-030011ace591',
          text: 'В работе',
        },
        {
          key: '1',
          value: 'ea5fa154-3e20-a88d-e053-040011ac4cc6',
          text: 'Готово к отправке',
        },
      ],
      sortable: false,
    },
  ],
  rows: [
    {
      dateReg: null,
      note: null,
      dateSigning: null,
      dateRelease: null,
      subject: null,
      directoryOutputId: null,
      dateLastChange: '16.11.2022 13:14:02',
      questions: '',
      regNumSigned: null,
      type: 'Заявка',
      dateCreate: '16.11.2022 13:14:02',
      dateSend: '15.12.2022 16:40:19',
      number: '1',
      userCreate: 'Кирюхин Евгений Валерьевич',
      regNum: null,
      key: '309fc7da-1703-4067-a455-d9199ab8e94c',
      dateReception: null,
      actual: 'Актуально',
      directoryInputId: null,
      inn: null,
      nestedTable: {
        tabs: [
          {
            label: 'Пункты заявки',
            key: '1',
            endpoint: 'krg3_request_item',
            tableData: null,
          },
          {
            label: 'Уведомления, связанные с заявкой',
            key: '4',
            endpoint: 'krg3_notice_for_request',
            tableData: null,
          },
        ],
      },
      rowId: {
        hasOutputFiles: false,
        isPrepared: true,
        isActual: true,
        additional: '№ 1, не привязанные к пунктам',
        fileLinks: [],
        hasNested: true,
        id: '309fc7da-1703-4067-a455-d9199ab8e94c',
        type: '1',
        haveMainFile: 0,
        hasInputFiles: false,
        isDifferentialAccessEnabled: false,
        isUpdatableStatus: false,
      },
      accessStamp: 'ДСП-инспектирование',
      requestInternalEdit: '',
      statusId: 'request_prepared',
      hint: {
        status: 'Готово к отправке',
      },
      status: 'Готово к отправке',
    },
  ],
};

// Данные для таба "Пункты заявки"
export const requestItemsData = [
  {
    number: '1',
    userCreate: 'Кирюхин Евгений Валерьевич',
    dateLastChange: '16.11.2022 13:14:03',
    _sortColumn: 1,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'PDF',
    key: 'b0775986-03a8-4750-9466-0b13a0c713a1',
    status: 'Готово к отправке',
    rowId: {
      number: '1',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'b0775986-03a8-4750-9466-0b13a0c713a1',
    },
  },
  {
    number: '2',
    userCreate: 'Петров П.П.',
    dateLastChange: '17.11.2022 10:25:15',
    _sortColumn: 2,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Дополнительный',
    fileFormat: 'DOCX',
    key: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
    status: 'В работе',
    rowId: {
      number: '2',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
    },
  },
  {
    number: '3',
    userCreate: 'Сидоров С.С.',
    dateLastChange: '18.11.2022 14:45:30',
    _sortColumn: 3,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'XLSX',
    key: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
    status: 'Готово к отправке',
    rowId: {
      number: '3',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
    },
  },
];

export const requestItemsColumns = [
  {
    title: 'Номер',
    dataIndex: 'number',
    key: 'number',
    columnType: 'String',
    width: 100,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Тип',
    dataIndex: 'type',
    key: 'type',
    columnType: 'String',
    width: 150,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Формат файла',
    dataIndex: 'fileFormat',
    key: 'fileFormat',
    columnType: 'String',
    width: 120,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Статус',
    dataIndex: 'status',
    key: 'status',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор',
    dataIndex: 'userCreate',
    key: 'userCreate',
    columnType: 'String',
    width: 250,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата изменения',
    dataIndex: 'dateLastChange',
    key: 'dateLastChange',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];

// Данные для таба "Уведомления, связанные с заявкой"
export const noticesForRequestData = [
  {
    actual: 'Актуально',
    dateReg: '16.11.2022 17:58:15',
    note: 'Уведомление о принятии заявки',
    dateSigning: null,
    dateRelease: null,
    subject: 'Принятие заявки №1',
    dateLastChange: '16.11.2022 17:58:15',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '16.11.2022 17:58:15',
    dateSend: '16.11.2022 18:00:00',
    rowId: {
      isActual: true,
      id: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    },
    number: 6,
    userCreate: 'Кирюхин Евгений Валерьевич',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-001-2022',
    _sortColumn: 6,
    key: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    status: 'Отправлено',
    dateReception: '16.11.2022 18:05:00',
  },
  {
    actual: 'Актуально',
    dateReg: '17.11.2022 09:15:30',
    note: 'Уведомление о дополнительных требованиях',
    dateSigning: null,
    dateRelease: null,
    subject: 'Дополнительные требования к заявке №1',
    dateLastChange: '17.11.2022 09:15:30',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '17.11.2022 09:15:30',
    dateSend: null,
    rowId: {
      isActual: true,
      id: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    },
    number: 7,
    userCreate: 'Петров П.П.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_prepared',
    regNum: 'УВ-002-2022',
    _sortColumn: 7,
    key: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    status: 'Подготовлено',
    dateReception: null,
  },
];

export const noticesForRequestColumns = [
  {
    title: 'Номер',
    dataIndex: 'number',
    key: 'number',
    columnType: 'String',
    width: 100,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Тема',
    dataIndex: 'subject',
    key: 'subject',
    columnType: 'String',
    width: 300,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Статус',
    dataIndex: 'status',
    key: 'status',
    columnType: 'String',
    width: 150,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата создания',
    dataIndex: 'dateCreate',
    key: 'dateCreate',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата отправки',
    dataIndex: 'dateSend',
    key: 'dateSend',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор',
    dataIndex: 'userCreate',
    key: 'userCreate',
    columnType: 'String',
    width: 250,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Регистрационный номер',
    dataIndex: 'regNum',
    key: 'regNum',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];

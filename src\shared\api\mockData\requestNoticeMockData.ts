// Моковые данные для requestNotice (ленивые табы)

export const requestNoticeTableData = {
  columns: [
    {
      title: 'Номер',
      dataIndex: 'number',
      key: 'number',
      columnType: 'String',
      width: 100,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Вид',
      dataIndex: 'type',
      key: 'type',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '1',
          value: '1',
          text: 'Заявка',
        },
        {
          key: '2',
          value: '2',
          text: 'Уведомление',
        },
      ],
      sortable: false,
    },
    {
      title: 'Статус',
      dataIndex: 'status',
      key: 'status',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '0',
          value: 'edaceb01-f2a9-3d29-e053-030011ace591',
          text: 'В работе',
        },
        {
          key: '1',
          value: 'ea5fa154-3e20-a88d-e053-040011ac4cc6',
          text: 'Готово к отправке',
        },
      ],
      sortable: false,
    },
  ],
  rows: [
    {
      dateReg: null,
      note: null,
      dateSigning: null,
      dateRelease: null,
      subject: null,
      directoryOutputId: null,
      dateLastChange: '16.11.2022 13:14:02',
      questions: '',
      regNumSigned: null,
      type: 'Заявка',
      dateCreate: '16.11.2022 13:14:02',
      dateSend: '15.12.2022 16:40:19',
      number: '1',
      userCreate: 'Кирюхин Евгений Валерьевич',
      regNum: null,
      key: '309fc7da-1703-4067-a455-d9199ab8e94c',
      dateReception: null,
      actual: 'Актуально',
      directoryInputId: null,
      inn: null,
      nestedTable: {
        tabs: [
          {
            label: 'Пункты заявки',
            key: '1',
            endpoint: 'krg3_request_item',
            tableData: null,
          },
          {
            label: 'Уведомления, связанные с заявкой',
            key: '4',
            endpoint: 'krg3_notice_for_request',
            tableData: null,
          },
        ],
      },
      rowId: {
        hasOutputFiles: false,
        isPrepared: true,
        isActual: true,
        additional: '№ 1, не привязанные к пунктам',
        fileLinks: [],
        hasNested: true,
        id: '309fc7da-1703-4067-a455-d9199ab8e94c',
        type: '1',
        haveMainFile: 0,
        hasInputFiles: false,
        isDifferentialAccessEnabled: false,
        isUpdatableStatus: false,
      },
      accessStamp: 'ДСП-инспектирование',
      requestInternalEdit: '',
      statusId: 'request_prepared',
      hint: {
        status: 'Готово к отправке',
      },
      status: 'Готово к отправке',
    },
  ],
};

// Данные для таба "Пункты заявки"
export const requestItemsData = [
  {
    number: '1',
    userCreate: 'Кирюхин Евгений Валерьевич',
    dateLastChange: '16.11.2022 13:14:03',
    _sortColumn: 1,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'PDF',
    key: 'b0775986-03a8-4750-9466-0b13a0c713a1',
    status: 'Готово к отправке',
    rowId: {
      number: '1',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'b0775986-03a8-4750-9466-0b13a0c713a1',
    },
  },
  {
    number: '2',
    userCreate: 'Петров П.П.',
    dateLastChange: '17.11.2022 10:25:15',
    _sortColumn: 2,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Дополнительный',
    fileFormat: 'DOCX',
    key: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
    status: 'В работе',
    rowId: {
      number: '2',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
    },
  },
  {
    number: '3',
    userCreate: 'Сидоров С.С.',
    dateLastChange: '18.11.2022 14:45:30',
    _sortColumn: 3,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'XLSX',
    key: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
    status: 'Готово к отправке',
    rowId: {
      number: '3',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
    },
  },
  {
    number: '4',
    userCreate: 'Иванов И.И.',
    dateLastChange: '19.11.2022 09:30:45',
    _sortColumn: 4,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'PDF',
    key: 'e3aa8bb9-36db-7a83-c799-3e46d3fa46d4',
    status: 'Готово к отправке',
    rowId: {
      number: '4',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'e3aa8bb9-36db-7a83-c799-3e46d3fa46d4',
    },
  },
  {
    number: '5',
    userCreate: 'Козлов К.К.',
    dateLastChange: '20.11.2022 16:15:20',
    _sortColumn: 5,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Дополнительный',
    fileFormat: 'DOCX',
    key: 'f4bb9cca-47ec-8b94-da8a-4f57e4gb57e5',
    status: 'В работе',
    rowId: {
      number: '5',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'f4bb9cca-47ec-8b94-da8a-4f57e4gb57e5',
    },
  },
  {
    number: '6',
    userCreate: 'Морозов М.М.',
    dateLastChange: '21.11.2022 11:45:10',
    _sortColumn: 6,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'XLSX',
    key: 'g5cc0ddb-58fd-9ca5-eb9b-5g68f5hc68f6',
    status: 'Готово к отправке',
    rowId: {
      number: '6',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'g5cc0ddb-58fd-9ca5-eb9b-5g68f5hc68f6',
    },
  },
  {
    number: '7',
    userCreate: 'Волков В.В.',
    dateLastChange: '22.11.2022 13:20:35',
    _sortColumn: 7,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'PDF',
    key: 'h6dd1eec-69ge-0db6-fc0c-6h79g6id79g7',
    status: 'В работе',
    rowId: {
      number: '7',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'h6dd1eec-69ge-0db6-fc0c-6h79g6id79g7',
    },
  },
  {
    number: '8',
    userCreate: 'Лебедев Л.Л.',
    dateLastChange: '23.11.2022 08:55:25',
    _sortColumn: 8,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Дополнительный',
    fileFormat: 'DOCX',
    key: 'i7ee2ffd-7ahf-1ec7-gd1d-7i80h7je80h8',
    status: 'Готово к отправке',
    rowId: {
      number: '8',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'i7ee2ffd-7ahf-1ec7-gd1d-7i80h7je80h8',
    },
  },
  {
    number: '9',
    userCreate: 'Соколов С.С.',
    dateLastChange: '24.11.2022 15:40:50',
    _sortColumn: 9,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'XLSX',
    key: 'j8ff3gge-8big-2fd8-he2e-8j91i8kf91i9',
    status: 'В работе',
    rowId: {
      number: '9',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'j8ff3gge-8big-2fd8-he2e-8j91i8kf91i9',
    },
  },
  {
    number: '10',
    userCreate: 'Новиков Н.Н.',
    dateLastChange: '25.11.2022 12:10:15',
    _sortColumn: 10,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'PDF',
    key: 'k9gg4hhf-9cjh-3ge9-if3f-9ka2j9lg02ja',
    status: 'Готово к отправке',
    rowId: {
      number: '10',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'k9gg4hhf-9cjh-3ge9-if3f-9ka2j9lg02ja',
    },
  },
  {
    number: '11',
    userCreate: 'Федоров Ф.Ф.',
    dateLastChange: '26.11.2022 14:25:40',
    _sortColumn: 11,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Дополнительный',
    fileFormat: 'DOCX',
    key: 'l0hh5iig-0dki-4hf0-jg4g-0lb3k0mh13kb',
    status: 'В работе',
    rowId: {
      number: '11',
      statusId: 'request_in_progress',
      fileLinks: [],
      id: 'l0hh5iig-0dki-4hf0-jg4g-0lb3k0mh13kb',
    },
  },
  {
    number: '12',
    userCreate: 'Михайлов М.М.',
    dateLastChange: '27.11.2022 10:50:30',
    _sortColumn: 12,
    directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
    type: 'Основной',
    fileFormat: 'XLSX',
    key: 'm1ii6jjh-1elj-5ig1-kh5h-1mc4l1ni24lc',
    status: 'Готово к отправке',
    rowId: {
      number: '12',
      statusId: 'request_prepared',
      fileLinks: [],
      id: 'm1ii6jjh-1elj-5ig1-kh5h-1mc4l1ni24lc',
    },
  },
];

export const requestItemsColumns = [
  {
    title: 'Номер',
    dataIndex: 'number',
    key: 'number',
    columnType: 'String',
    width: 100,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Тип',
    dataIndex: 'type',
    key: 'type',
    columnType: 'String',
    width: 150,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Формат файла',
    dataIndex: 'fileFormat',
    key: 'fileFormat',
    columnType: 'String',
    width: 120,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Статус',
    dataIndex: 'status',
    key: 'status',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор',
    dataIndex: 'userCreate',
    key: 'userCreate',
    columnType: 'String',
    width: 250,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата изменения',
    dataIndex: 'dateLastChange',
    key: 'dateLastChange',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];

// Данные для таба "Уведомления, связанные с заявкой"
export const noticesForRequestData = [
  {
    actual: 'Актуально',
    dateReg: '16.11.2022 17:58:15',
    note: 'Уведомление о принятии заявки',
    dateSigning: null,
    dateRelease: null,
    subject: 'Принятие заявки №1',
    dateLastChange: '16.11.2022 17:58:15',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '16.11.2022 17:58:15',
    dateSend: '16.11.2022 18:00:00',
    rowId: {
      isActual: true,
      id: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    },
    number: 6,
    userCreate: 'Кирюхин Евгений Валерьевич',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-001-2022',
    _sortColumn: 6,
    key: '3cdf63ae-450e-4e3a-a08c-57f8b32e8fcc',
    status: 'Отправлено',
    dateReception: '16.11.2022 18:05:00',
  },
  {
    actual: 'Актуально',
    dateReg: '17.11.2022 09:15:30',
    note: 'Уведомление о дополнительных требованиях',
    dateSigning: null,
    dateRelease: null,
    subject: 'Дополнительные требования к заявке №1',
    dateLastChange: '17.11.2022 09:15:30',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '17.11.2022 09:15:30',
    dateSend: null,
    rowId: {
      isActual: true,
      id: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    },
    number: 7,
    userCreate: 'Петров П.П.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_prepared',
    regNum: 'УВ-002-2022',
    _sortColumn: 7,
    key: '4def74bf-561f-5b4b-b19d-68f9c43f9ddd',
    status: 'Подготовлено',
    dateReception: null,
  },
  {
    actual: 'Актуально',
    dateReg: '18.11.2022 14:30:20',
    note: 'Уведомление о статусе рассмотрения',
    dateSigning: null,
    dateRelease: null,
    subject: 'Статус рассмотрения заявки №1',
    dateLastChange: '18.11.2022 14:30:20',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '18.11.2022 14:30:20',
    dateSend: '18.11.2022 15:00:00',
    rowId: {
      isActual: true,
      id: '5efg85cf-672g-6c5c-b20d-68g9d54g9eee',
    },
    number: 8,
    userCreate: 'Иванов И.И.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-003-2022',
    _sortColumn: 8,
    key: '5efg85cf-672g-6c5c-b20d-68g9d54g9eee',
    status: 'Отправлено',
    dateReception: '18.11.2022 15:10:00',
  },
  {
    actual: 'Актуально',
    dateReg: '19.11.2022 10:45:15',
    note: 'Уведомление о завершении экспертизы',
    dateSigning: null,
    dateRelease: null,
    subject: 'Завершение экспертизы заявки №1',
    dateLastChange: '19.11.2022 10:45:15',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '19.11.2022 10:45:15',
    dateSend: null,
    rowId: {
      isActual: true,
      id: '6fgh96dg-783h-7d6d-c31e-79h0e65h0fff',
    },
    number: 9,
    userCreate: 'Сидоров С.С.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_prepared',
    regNum: 'УВ-004-2022',
    _sortColumn: 9,
    key: '6fgh96dg-783h-7d6d-c31e-79h0e65h0fff',
    status: 'Подготовлено',
    dateReception: null,
  },
  {
    actual: 'Актуально',
    dateReg: '20.11.2022 16:20:30',
    note: 'Уведомление о результатах проверки',
    dateSigning: null,
    dateRelease: null,
    subject: 'Результаты проверки заявки №1',
    dateLastChange: '20.11.2022 16:20:30',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '20.11.2022 16:20:30',
    dateSend: '20.11.2022 17:00:00',
    rowId: {
      isActual: true,
      id: '7ghi07eh-894i-8e7e-d42f-80i1f76i1ggg',
    },
    number: 10,
    userCreate: 'Козлов К.К.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-005-2022',
    _sortColumn: 10,
    key: '7ghi07eh-894i-8e7e-d42f-80i1f76i1ggg',
    status: 'Отправлено',
    dateReception: '20.11.2022 17:15:00',
  },
  {
    actual: 'Актуально',
    dateReg: '21.11.2022 11:55:45',
    note: 'Уведомление о необходимости доработки',
    dateSigning: null,
    dateRelease: null,
    subject: 'Необходимость доработки заявки №1',
    dateLastChange: '21.11.2022 11:55:45',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '21.11.2022 11:55:45',
    dateSend: null,
    rowId: {
      isActual: true,
      id: '8hij18fi-905j-9f8f-e53g-91j2g87j2hhh',
    },
    number: 11,
    userCreate: 'Морозов М.М.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_prepared',
    regNum: 'УВ-006-2022',
    _sortColumn: 11,
    key: '8hij18fi-905j-9f8f-e53g-91j2g87j2hhh',
    status: 'Подготовлено',
    dateReception: null,
  },
  {
    actual: 'Актуально',
    dateReg: '22.11.2022 13:40:20',
    note: 'Уведомление о принятии к рассмотрению',
    dateSigning: null,
    dateRelease: null,
    subject: 'Принятие к рассмотрению заявки №1',
    dateLastChange: '22.11.2022 13:40:20',
    inn: '1234567890',
    regNumSigned: null,
    dateCreate: '22.11.2022 13:40:20',
    dateSend: '22.11.2022 14:00:00',
    rowId: {
      isActual: true,
      id: '9ijk29gj-016k-0g9g-f64h-02k3h98k3iii',
    },
    number: 12,
    userCreate: 'Волков В.В.',
    accessStamp: 'ДСП-инспектирование',
    statusId: 'notice_sent',
    regNum: 'УВ-007-2022',
    _sortColumn: 12,
    key: '9ijk29gj-016k-0g9g-f64h-02k3h98k3iii',
    status: 'Отправлено',
    dateReception: '22.11.2022 14:20:00',
  },
];

export const noticesForRequestColumns = [
  {
    title: 'Номер',
    dataIndex: 'number',
    key: 'number',
    columnType: 'String',
    width: 100,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Тема',
    dataIndex: 'subject',
    key: 'subject',
    columnType: 'String',
    width: 300,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Статус',
    dataIndex: 'status',
    key: 'status',
    columnType: 'String',
    width: 150,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата создания',
    dataIndex: 'dateCreate',
    key: 'dateCreate',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата отправки',
    dataIndex: 'dateSend',
    key: 'dateSend',
    columnType: 'String',
    width: 180,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор',
    dataIndex: 'userCreate',
    key: 'userCreate',
    columnType: 'String',
    width: 250,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Регистрационный номер',
    dataIndex: 'regNum',
    key: 'regNum',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];

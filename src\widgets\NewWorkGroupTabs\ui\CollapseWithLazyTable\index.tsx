import { Collapse, Pagination } from 'antd';
import { ReactNode, useState } from 'react';
import { CollapseWithLazyTableProps, NWGLib } from 'widgets/NewWorkGroupTabs';
import {
  DataGrid,
  NestedTabsWithLazyTable,
  TableRowData,
  TabsWithLazyTable,
} from 'features/DataGrid';

import { ApiContainer } from 'shared/ui';
import styles from './styles.module.scss';

export const collapseWithLazyTable: CollapseWithLazyTableProps = (
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
): ReactNode => {
  const { renderCollapseTitle, customColumnsWidth } = NWGLib;
  const [activeTabKeys, setActiveTabKeys] = useState<string[]>([]);

  const handleTabToggle = async (key: string | string[]) => {
    const keys = Array.isArray(key) ? key : [key];
    setActiveTabKeys(keys);

    // Загружаем содержимое для новых открытых табов
    for (const tabKey of keys) {
      if (!activeTabKeys.includes(tabKey)) {
        const tab = nestedContent.tabs.find((t) => t.key === tabKey);
        if (tab && !tab.tableData && !tab.tabStatus?.isLoading) {
          await getLazyTabContent(parentRow, tabKey);
        }
      }
    }
  };

  const handlePageChange = async (
    tab: TabsWithLazyTable,
    page: number,
  ): Promise<void> => {
    await getLazyTabContent(parentRow, tab.key, page);
  };

  return (
    <ApiContainer
      error={parentRow.nestedTableStatus?.error as AppError}
      isPending={!!parentRow.nestedTableStatus?.isLoading}
    >
      <Collapse
        className={styles.collapse}
        activeKey={activeTabKeys}
        onChange={handleTabToggle}
      >
        {nestedContent.tabs.map((tab) => (
          <Collapse.Panel
            key={tab.key}
            header={renderCollapseTitle(
              tab.label,
              parentRow.rowId?.additional || '',
            )}
          >
            <ApiContainer
              error={tab.tabStatus?.error as AppError}
              isPending={!!tab.tabStatus?.isLoading}
            >
              {tab.tableData && (
                <>
                  <DataGrid
                    resizableProps={{ isActive: true }}
                    additionalClassNames={{
                      table: styles.nestedTable,
                      container: styles.nestedTable,
                    }}
                    columns={
                      Array.isArray(tab.tableData.columns)
                        ? tab.tableData.columns.map((column) => ({
                            ...column,
                            ...(column.columnType !== 'String' && {
                              fixed: 'right',
                              width: customColumnsWidth(column?.columnType || ''),
                              align: 'center',
                              hideSorter: true,
                              hideColumnSearch: true,
                            }),
                            render: (text: string, row: TableRowData) =>
                              customRowRender(
                                text,
                                row,
                                column,
                                refetch,
                                togglePopup,
                                handleRow,
                                activeEndpoint,
                                permissions,
                                isRequest,
                              ),
                          }))
                        : []
                    }
                    rows={
                      Array.isArray(tab.tableData.rows) ? tab.tableData.rows : []
                    }
                    tableAdditionProps={{
                      size: 'small',
                      scroll: { x: '100%', y: '100%' },
                      pagination: false, // Отключаем встроенную пагинацию
                    }}
                  />
                  {tab.pagination && tab.pagination.total > tab.pagination.pageSize && (
                    <div className={styles.paginationContainer}>
                      <Pagination
                        current={tab.pagination.currentPage}
                        total={tab.pagination.total}
                        pageSize={tab.pagination.pageSize}
                        showSizeChanger={false}
                        onChange={(page) => handlePageChange(tab, page)}
                        size="small"
                      />
                    </div>
                  )}
                </>
              )}
            </ApiContainer>
          </Collapse.Panel>
        ))}
      </Collapse>
    </ApiContainer>
  );
};

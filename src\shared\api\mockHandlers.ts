/* eslint-disable import/no-internal-modules */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';
import {
  eppTreeMockData,
  krgTreeRootMockData,
  reestry,
  resultaty,
} from './mockData/eppTreeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';

// const fileNethandlers = [
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/get/11743`,
//     async () => {
//       await delay();
//       return HttpResponse.json(fileCard, { status: 200 });
//     },
//   ),
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/v2/get/as/tree`,
//     async () => {
//       await delay(3000);
//       return HttpResponse.json(fileTree, { status: 200 });
//     },
//   ),
//   http.post(
//     `${devServerUrl}${FILENET_SERVICE}/main/find/documents`,
//     async () => {
//       await delay();
//       return HttpResponse.json(filesList, { status: 200 });
//     },
//   ),
// ];

const mockHandlers = [
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay();
    return HttpResponse.json(
      {
        ...krgTableMockData,
        // You can use page and size here if needed for pagination
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // Мок для основной таблицы packageDef с ленивыми табами
  http.post('http://127.0.0.1:18080/kzid_rest/krg3_input_package_def', async ({ request }) => {
    const url = new URL(request.url);
    const pageNumber = url.searchParams.get('pageNumber') || '1';
    const pageSize = url.searchParams.get('pageSize') || '10';

    await delay(500);
    return HttpResponse.json({
      columns: [
        {
          title: "Номер",
          dataIndex: "number",
          key: "number",
          columnType: "String",
          width: 100,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Статус",
          dataIndex: "status",
          key: "status",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Дата регистрации или отказа в САДД",
          dataIndex: "dateRegOrDissSadd",
          key: "dateRegOrDissSadd",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        }
      ],
      rows: [
        {
          number: 1,
          status: "Принят в КРГ",
          dateRegOrDissSadd: "15.10.2022 11:22:10",
          dateUploadRegPackage: "16.11.2022 18:28:07",
          dateLastChange: "16.11.2022 18:28:07",
          requests: "",
          id: "701d1479-05d5-4f61-a13d-de3acce42ce3",
          dateSendRegPackage: "16.10.2022 11:22:10",
          nestedTable: {
            tabs: [
              {
                label: "Файлы в составе пакета",
                key: "1",
                endpoint: "krg3_package_files_def",
                tableData: null
              }
            ]
          },
          key: "701d1479-05d5-4f61-a13d-de3acce42ce3",
          rowId: {
            id: "701d1479-05d5-4f61-a13d-de3acce42ce3",
            hasNested: true
          }
        },
        {
          number: 2,
          status: "В обработке",
          dateRegOrDissSadd: "20.10.2022 14:30:15",
          dateUploadRegPackage: "21.11.2022 10:15:30",
          dateLastChange: "21.11.2022 10:15:30",
          requests: "",
          id: "802e2580-16e6-5f72-b24e-ef4bddf53df4",
          dateSendRegPackage: "21.10.2022 14:30:15",
          nestedTable: {
            tabs: [
              {
                label: "Файлы в составе пакета",
                key: "1",
                endpoint: "krg3_package_files_def",
                tableData: null
              }
            ]
          },
          key: "802e2580-16e6-5f72-b24e-ef4bddf53df4",
          rowId: {
            id: "802e2580-16e6-5f72-b24e-ef4bddf53df4",
            hasNested: true
          }
        }
      ],
      pagination: {
        total: 3,
        pageSize: parseInt(pageSize, 10)
      },
      pageNumber: parseInt(pageNumber, 10),
      sort: null,
      group: null
    }, { status: 200 });
  }),

  // Мок для загрузки списка табов (ленивая загрузка)
  http.post('http://127.0.0.1:18080/kzid_rest/krg3_input_package_def/tabs', async () => {
    await delay(300);
    return HttpResponse.json({
      tabs: [
        {
          label: "Файлы в составе пакета",
          key: "1",
          endpoint: "krg3_package_files_def",
          tableData: null
        }
      ]
    }, { status: 200 });
  }),

  // Мок для загрузки содержимого таба с пагинацией
  http.post('http://127.0.0.1:18080/kzid_rest/krg3_package_files_def', async ({ request }) => {
    const url = new URL(request.url);
    const pageNumber = parseInt(url.searchParams.get('pageNumber') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    await delay(800); // Имитируем загрузку

    // Генерируем данные для разных страниц
    const allFiles = [
      {
        downloadButton: "",
        isOmni: "ОМНИ",
        isMain: 0,
        ukepId: null,
        unlinkDef: "",
        dateUpload: "19.12.2022 15:08:54",
        signAuthor: null,
        rowId: {
          canView: true,
          format: "rar",
          isFileSignExist: false,
          canDownload: true,
          id: "a16247e8-5180-41d1-a014-9fbd25862aab",
          isRemovable: false,
          enableUnlinkDef: true
        },
        size: 68.95,
        name: "xsd.rar",
        unepId: null,
        cabinet: "/Пакет 2",
        requestNotice: "",
        key: "a16247e8-5180-41d1-a014-9fbd25862aab"
      },
      {
        downloadButton: "",
        isOmni: "ОМНИ",
        isMain: 0,
        ukepId: null,
        unlinkDef: "",
        dateUpload: "19.12.2022 15:01:43",
        signAuthor: null,
        rowId: {
          canView: true,
          format: "txt",
          isFileSignExist: false,
          canDownload: true,
          id: "a6b43ab7-1c47-44ad-ba44-dcddaa553609",
          isRemovable: false,
          enableUnlinkDef: true
        },
        size: 8.94,
        name: "test.txt",
        unepId: null,
        cabinet: "/Пакет 2",
        requestNotice: "",
        key: "a6b43ab7-1c47-44ad-ba44-dcddaa553609"
      },
      {
        downloadButton: "",
        isOmni: "ОМНИ",
        isMain: 1,
        ukepId: "12345",
        unlinkDef: "",
        dateUpload: "20.12.2022 10:15:30",
        signAuthor: "Иванов И.И.",
        rowId: {
          canView: true,
          format: "pdf",
          isFileSignExist: true,
          canDownload: true,
          id: "b7c54bc8-2d58-45be-bb55-ecdebb664710",
          isRemovable: false,
          enableUnlinkDef: true
        },
        size: 125.67,
        name: "document.pdf",
        unepId: "67890",
        cabinet: "/Пакет 2",
        requestNotice: "",
        key: "b7c54bc8-2d58-45be-bb55-ecdebb664710"
      },
      {
        downloadButton: "",
        isOmni: "САДД",
        isMain: 0,
        ukepId: null,
        unlinkDef: "",
        dateUpload: "21.12.2022 14:22:15",
        signAuthor: null,
        rowId: {
          canView: true,
          format: "docx",
          isFileSignExist: false,
          canDownload: true,
          id: "c8d65cd9-3e69-56cf-cc66-fdefc775821",
          isRemovable: true,
          enableUnlinkDef: true
        },
        size: 45.23,
        name: "report.docx",
        unepId: null,
        cabinet: "/Пакет 2",
        requestNotice: "",
        key: "c8d65cd9-3e69-56cf-cc66-fdefc775821"
      },
      {
        downloadButton: "",
        isOmni: "ОМНИ",
        isMain: 0,
        ukepId: null,
        unlinkDef: "",
        dateUpload: "22.12.2022 09:45:12",
        signAuthor: null,
        rowId: {
          canView: true,
          format: "xlsx",
          isFileSignExist: false,
          canDownload: true,
          id: "d9e76dea-4f7a-67d0-dd77-0ef0d886932",
          isRemovable: false,
          enableUnlinkDef: true
        },
        size: 78.12,
        name: "data.xlsx",
        unepId: null,
        cabinet: "/Пакет 2",
        requestNotice: "",
        key: "d9e76dea-4f7a-67d0-dd77-0ef0d886932"
      }
    ];

    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageFiles = allFiles.slice(startIndex, endIndex);

    return HttpResponse.json({
      columns: [
        {
          title: "Имя",
          dataIndex: "name",
          key: "name",
          columnType: "String",
          width: 300,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Расположение файла",
          dataIndex: "cabinet",
          key: "cabinet",
          columnType: "String",
          width: 600,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Размер, кБ",
          dataIndex: "size",
          key: "size",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Номер заявки",
          dataIndex: "requestNotice",
          key: "requestNotice",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Основной",
          dataIndex: "isMain",
          key: "isMain",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "УКЭП",
          dataIndex: "ukepId",
          key: "ukepId",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Автор подписи",
          dataIndex: "signAuthor",
          key: "signAuthor",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "УНЭП",
          dataIndex: "unepId",
          key: "unepId",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Дата загрузки файла",
          dataIndex: "dateUpload",
          key: "dateUpload",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Источник",
          dataIndex: "isOmni",
          key: "isOmni",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Привязка файла",
          dataIndex: "unlinkDef",
          key: "unlinkDef",
          columnType: "unlinkDef",
          width: 40,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Просмотр и выгрузка",
          dataIndex: "downloadButton",
          key: "downloadButton",
          columnType: "downloadButton",
          width: 40,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        }
      ],
      rows: pageFiles,
      pagination: {
        total: allFiles.length,
        pageSize,
      },
      pageNumber,
      sort: null,
      group: null
    }, { status: 200 });
  }),

  // Мок для основной таблицы request_notice с ленивыми табами
  http.post('http://127.0.0.1:18080/kzid_rest/krg3_request_notice', async ({ request }) => {
    const url = new URL(request.url);
    const pageNumber = url.searchParams.get('pageNumber') || '1';
    const pageSize = url.searchParams.get('pageSize') || '10';

    await delay(500);
    return HttpResponse.json({
      columns: [
        {
          title: "Номер",
          dataIndex: "number",
          key: "number",
          columnType: "String",
          width: 100,
          align: "left",
          columnUuid: null,
          filterType: null,
          filters: null,
          sortable: false
        },
        {
          title: "Вид",
          dataIndex: "type",
          key: "type",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: "default",
          filters: [
            {
              key: "1",
              value: "1",
              text: "Заявка"
            },
            {
              key: "2",
              value: "2",
              text: "Уведомление"
            }
          ],
          sortable: false
        },
        {
          title: "Статус",
          dataIndex: "status",
          key: "status",
          columnType: "String",
          width: 200,
          align: "left",
          columnUuid: null,
          filterType: "default",
          filters: [
            {
              key: "0",
              value: "edaceb01-f2a9-3d29-e053-030011ace591",
              text: "В работе"
            },
            {
              key: "1",
              value: "ea5fa154-3e20-a88d-e053-040011ac4cc6",
              text: "Готово к отправке"
            }
          ],
          sortable: false
        }
      ],
      rows: [
        {
          dateReg: null,
          note: null,
          dateSigning: null,
          dateRelease: null,
          subject: null,
          directoryOutputId: null,
          dateLastChange: "16.11.2022 13:14:02",
          questions: "",
          regNumSigned: null,
          type: "Заявка",
          dateCreate: "16.11.2022 13:14:02",
          dateSend: "15.12.2022 16:40:19",
          number: "1",
          userCreate: "Кирюхин Евгений Валерьевич",
          regNum: null,
          key: "309fc7da-1703-4067-a455-d9199ab8e94c",
          dateReception: null,
          actual: "Актуально",
          directoryInputId: null,
          inn: null,
          nestedTable: {
            tabs: [
              {
                label: "Пункты заявки",
                key: "1",
                endpoint: "krg3_request_item",
                tableData: null
              },
              {
                label: "Уведомления, связанные с заявкой",
                key: "4",
                endpoint: "krg3_notice_for_request",
                tableData: null
              }
            ]
          },
          rowId: {
            hasOutputFiles: false,
            isPrepared: true,
            isActual: true,
            additional: "№ 1, не привязанные к пунктам",
            fileLinks: [],
            hasNested: true,
            id: "309fc7da-1703-4067-a455-d9199ab8e94c",
            type: "1",
            haveMainFile: 0,
            hasInputFiles: false,
            isDifferentialAccessEnabled: false,
            isUpdatableStatus: false
          },
          accessStamp: "ДСП-инспектирование",
          requestInternalEdit: "",
          statusId: "request_prepared",
          hint: {
            status: "Готово к отправке"
          },
          status: "Готово к отправке"
        }
      ],
      pagination: {
        total: 9,
        pageSize: parseInt(pageSize, 10)
      },
      pageNumber: parseInt(pageNumber, 10),
      sort: null,
      group: null
    }, { status: 200 });
  }),

  // Мок для загрузки списка табов для request_notice
  http.post('http://127.0.0.1:18080/kzid_rest/krg3_request_notice/tabs', async () => {
    await delay(300);
    return HttpResponse.json({
      tabs: [
        {
          label: "Пункты заявки",
          key: "1",
          endpoint: "krg3_request_item",
          tableData: null
        },
        {
          label: "Уведомления, связанные с заявкой",
          key: "4",
          endpoint: "krg3_notice_for_request",
          tableData: null
        }
      ]
    }, { status: 200 });
  }),
];

const time = 1000;

const eppTreeMockHandlers = [
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const pos = url.searchParams.get('pos');
      const cabinetId = url.searchParams.get('cabinetId');
      const user = url.searchParams.get('user');
      const isCabinet = url.searchParams.get('isCabinet') === 'true';

      // Only mock requests with specific parameters
      if (
        cabinetId === 'bd44a562-fcae-45b6-b38f-539e7b69ade9' &&
        user === 'admin' &&
        isCabinet === true
      ) {
        await delay(time);
        return HttpResponse.json(
          {
            ...krgTreeRootMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '4109a8e1-7b82-44b5-b33a-ee978c425517') {
        await delay(time);
        return HttpResponse.json(
          {
            ...eppTreeMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '389c9fe9-b118-4773-8e2a-327a183a4763') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '1af7bc7c-57e7-4d69-992b-16f635a0fe02') {
        await delay(time);
        return HttpResponse.json(
          {
            ...resultaty,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      // For requests with different parameters, don't return a response
      // This will cause MSW to pass through to the real API
      return undefined;
    },
  ),

  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async ({ request }) => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),
];

export const handlers = [...mockHandlers, ...eppTreeMockHandlers];

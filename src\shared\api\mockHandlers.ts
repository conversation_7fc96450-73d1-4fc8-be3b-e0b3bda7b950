/* eslint-disable import/no-internal-modules */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';
import { allFiles } from './mockData/allfiles';
import { columnsPackageFilesDef } from './mockData/columnsPackageFilesDef';
import {
  eppTreeMockData,
  krgTreeRootMockData,
  reestry,
  resultaty,
} from './mockData/eppTreeMockData';
import { krg3RequestNoticeMockData } from './mockData/krg3RequestNoticeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';
import { krg3NoticeForRequestMockData } from './mockData/notices';

// const fileNethandlers = [
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/get/11743`,
//     async () => {
//       await delay();
//       return HttpResponse.json(fileCard, { status: 200 });
//     },
//   ),
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/v2/get/as/tree`,
//     async () => {
//       await delay(3000);
//       return HttpResponse.json(fileTree, { status: 200 });
//     },
//   ),
//   http.post(
//     `${devServerUrl}${FILENET_SERVICE}/main/find/documents`,
//     async () => {
//       await delay();
//       return HttpResponse.json(filesList, { status: 200 });
//     },
//   ),
// ];

const mockHandlers = [
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay();
    return HttpResponse.json(
      {
        ...krgTableMockData,
        // You can use page and size here if needed for pagination
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // Мок для основной таблицы packageDef с ленивыми табами
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_input_package_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(500);
      return HttpResponse.json(
        {
          columns: [
            {
              title: 'Номер',
              dataIndex: 'number',
              key: 'number',
              columnType: 'String',
              width: 100,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Статус',
              dataIndex: 'status',
              key: 'status',
              columnType: 'String',
              width: 200,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Дата регистрации или отказа в САДД',
              dataIndex: 'dateRegOrDissSadd',
              key: 'dateRegOrDissSadd',
              columnType: 'String',
              width: 200,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
          ],
          rows: [
            {
              number: 1,
              status: 'Принят в КРГ',
              dateRegOrDissSadd: '15.10.2022 11:22:10',
              dateUploadRegPackage: '16.11.2022 18:28:07',
              dateLastChange: '16.11.2022 18:28:07',
              requests: '',
              id: '701d1479-05d5-4f61-a13d-de3acce42ce3',
              dateSendRegPackage: '16.10.2022 11:22:10',
              nestedTable: {
                tabs: [
                  {
                    label: 'Файлы в составе пакета',
                    key: '1',
                    endpoint: 'krg3_package_files_def',
                    tableData: null,
                  },
                ],
              },
              key: '701d1479-05d5-4f61-a13d-de3acce42ce3',
              rowId: {
                id: '701d1479-05d5-4f61-a13d-de3acce42ce3',
                hasNested: true,
              },
            },
            {
              number: 2,
              status: 'В обработке',
              dateRegOrDissSadd: '20.10.2022 14:30:15',
              dateUploadRegPackage: '21.11.2022 10:15:30',
              dateLastChange: '21.11.2022 10:15:30',
              requests: '',
              id: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
              dateSendRegPackage: '21.10.2022 14:30:15',
              nestedTable: {
                tabs: [
                  {
                    label: 'Файлы в составе пакета',
                    key: '1',
                    endpoint: 'krg3_package_files_def',
                    tableData: null,
                  },
                ],
              },
              key: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
              rowId: {
                id: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
                hasNested: true,
              },
            },
          ],
          pagination: {
            total: 3,
            pageSize: parseInt(pageSize, 10),
          },
          pageNumber: parseInt(pageNumber, 10),
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Мок для загрузки содержимого таба с пагинацией
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_package_files_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(800); // Имитируем загрузку

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageFiles = allFiles.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: columnsPackageFilesDef,
          rows: pageFiles,
          pagination: {
            total: allFiles.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Мок для основной таблицы request_notice с ленивыми табами
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      return HttpResponse.json(
        krg3RequestNoticeMockData(pageNumber, pageSize),
        { status: 200 },
      );
    },
  ),

  // Мок для загрузки содержимого таба "Пункты заявки"
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_item',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(600); // Имитируем загрузку

      // Генерируем данные для пунктов заявки
      const allItems = [
        {
          number: '1',
          userCreate: 'Кирюхин Евгений Валерьевич',
          dateLastChange: '16.11.2022 13:14:03',
          _sortColumn: 1,
          directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
          type: 'Основной',
          fileFormat: 'PDF',
          key: 'b0775986-03a8-4750-9466-0b13a0c713a1',
          status: 'Готово к отправке',
          rowId: {
            number: '1',
            statusId: 'request_prepared',
            fileLinks: [],
            id: 'b0775986-03a8-4750-9466-0b13a0c713a1',
          },
        },
        {
          number: '2',
          userCreate: 'Петров П.П.',
          dateLastChange: '17.11.2022 10:25:15',
          _sortColumn: 2,
          directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
          type: 'Дополнительный',
          fileFormat: 'DOCX',
          key: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
          status: 'В работе',
          rowId: {
            number: '2',
            statusId: 'request_in_progress',
            fileLinks: [],
            id: 'c1886a97-14b9-5861-a577-1c24b1d824b2',
          },
        },
        {
          number: '3',
          userCreate: 'Сидоров С.С.',
          dateLastChange: '18.11.2022 14:45:30',
          _sortColumn: 3,
          directoryId: '/L451712d/Материалы_20240926_151305/Коды вопросов',
          type: 'Основной',
          fileFormat: 'XLSX',
          key: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
          status: 'Готово к отправке',
          rowId: {
            number: '3',
            statusId: 'request_prepared',
            fileLinks: [],
            id: 'd2997ba8-25ca-6972-b688-2d35c2e935c3',
          },
        },
      ];

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageItems = allItems.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: [
            {
              title: 'Номер',
              dataIndex: 'number',
              key: 'number',
              columnType: 'String',
              width: 100,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Тип',
              dataIndex: 'type',
              key: 'type',
              columnType: 'String',
              width: 150,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Формат файла',
              dataIndex: 'fileFormat',
              key: 'fileFormat',
              columnType: 'String',
              width: 120,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Статус',
              dataIndex: 'status',
              key: 'status',
              columnType: 'String',
              width: 200,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Автор',
              dataIndex: 'userCreate',
              key: 'userCreate',
              columnType: 'String',
              width: 250,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
            {
              title: 'Дата изменения',
              dataIndex: 'dateLastChange',
              key: 'dateLastChange',
              columnType: 'String',
              width: 180,
              align: 'left',
              columnUuid: null,
              filterType: null,
              filters: null,
              sortable: false,
            },
          ],
          rows: pageItems,
          pagination: {
            total: allItems.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Мок для загрузки содержимого таба "Уведомления, связанные с заявкой"
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_notice_for_request',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(700); // Имитируем загрузку

      return HttpResponse.json(
        krg3NoticeForRequestMockData(Number(pageNumber), pageSize),
        { status: 200 },
      );
    },
  ),
];

const time = 1000;

const eppTreeMockHandlers = [
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const pos = url.searchParams.get('pos');
      const cabinetId = url.searchParams.get('cabinetId');
      const user = url.searchParams.get('user');
      const isCabinet = url.searchParams.get('isCabinet') === 'true';

      // Only mock requests with specific parameters
      if (
        cabinetId === 'bd44a562-fcae-45b6-b38f-539e7b69ade9' &&
        user === 'admin' &&
        isCabinet === true
      ) {
        await delay(time);
        return HttpResponse.json(
          {
            ...krgTreeRootMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '4109a8e1-7b82-44b5-b33a-ee978c425517') {
        await delay(time);
        return HttpResponse.json(
          {
            ...eppTreeMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '389c9fe9-b118-4773-8e2a-327a183a4763') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '1af7bc7c-57e7-4d69-992b-16f635a0fe02') {
        await delay(time);
        return HttpResponse.json(
          {
            ...resultaty,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      // For requests with different parameters, don't return a response
      // This will cause MSW to pass through to the real API
      return undefined;
    },
  ),

  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async ({ request }) => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),
];

export const handlers = [...mockHandlers, ...eppTreeMockHandlers];
